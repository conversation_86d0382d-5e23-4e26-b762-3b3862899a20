using QuantConnect.Data;
using QuantConnect.Interfaces;
using QuantConnect.Securities;

namespace QuantConnect.Brokerages.Mexc
{
    [BrokerageFactory(typeof(MexcFuturesBrokerageFactory))]
    public class MexcFuturesBrokerage : MexcBrokerage
    {
        public MexcFuturesBrokerage() : base("mexc")
        {
        }

        public MexcFuturesBrokerage(IDataAggregator aggregator) : base("mexc")
        {
        }

        protected override SecurityType GetSupportedSecurityType()
        {
            return SecurityType.CryptoFuture;
        }
    }
}

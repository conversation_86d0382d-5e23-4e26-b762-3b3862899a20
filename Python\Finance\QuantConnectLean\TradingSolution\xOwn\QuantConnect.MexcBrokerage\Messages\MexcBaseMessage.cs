using Newtonsoft.Json;

namespace QuantConnect.Brokerages.Mexc.Messages
{
    public class MexcBaseMessage
    {
        [JsonProperty("channel")]
        public string Channel { get; set; }

        [JsonProperty("symbol")]
        public string Symbol { get; set; }

        [JsonProperty("ts")]
        public long Timestamp { get; set; }
    }

    public class MexcSubscriptionMessage
    {
        [JsonProperty("method")]
        public string Method { get; set; }

        [JsonProperty("param")]
        public object Param { get; set; }
    }

    public class MexcSubscriptionParam
    {
        [JsonProperty("symbol")]
        public string Symbol { get; set; }
    }

    public class MexcPingMessage
    {
        [JsonProperty("method")]
        public string Method { get; set; } = "ping";
    }

    public class MexcPongMessage
    {
        [JsonProperty("channel")]
        public string Channel { get; set; }

        [JsonProperty("data")]
        public long Data { get; set; }
    }
}

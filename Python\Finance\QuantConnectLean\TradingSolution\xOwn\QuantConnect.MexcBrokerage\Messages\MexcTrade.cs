using Newtonsoft.Json;

namespace QuantConnect.Brokerages.Mexc.Messages
{
    public class MexcTrade
    {
        [JsonProperty("p")]
        public decimal Price { get; set; }

        [JsonProperty("v")]
        public decimal Volume { get; set; }

        [JsonProperty("T")]
        public int Side { get; set; }

        [JsonProperty("O")]
        public int OpenPosition { get; set; }

        [JsonProperty("M")]
        public int SelfTransact { get; set; }

        [JsonProperty("t")]
        public long Timestamp { get; set; }
    }

    public class MexcTradeMessage : MexcBaseMessage
    {
        [JsonProperty("data")]
        public MexcTrade Data { get; set; }
    }
}

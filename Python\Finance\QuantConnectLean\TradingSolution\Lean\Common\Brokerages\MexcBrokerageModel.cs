using System;
using System.Collections.Generic;
using System.Linq;
using QuantConnect.Benchmarks;
using QuantConnect.Orders.Fees;
using QuantConnect.Securities;
using QuantConnect.Util;

namespace QuantConnect.Brokerages
{
    public class MexcBrokerageModel : DefaultBrokerageModel
    {
        private const decimal _defaultLeverage = 3;

        protected virtual string MarketName => Market.MEXC;

        public override IReadOnlyDictionary<SecurityType, string> DefaultMarkets { get; } = GetDefaultMarkets(Market.MEXC);

        public MexcBrokerageModel(AccountType accountType = AccountType.Cash) : base(accountType)
        {
        }

        public override decimal GetLeverage(Security security)
        {
            if (AccountType == AccountType.Cash || security.IsInternalFeed() || security.Type == SecurityType.Base)
            {
                return 1m;
            }

            return _defaultLeverage;
        }

        public override IBenchmark GetBenchmark(SecurityManager securities)
        {
            return new FuncBenchmark(x => 0);
        }

        public override IFeeModel GetFeeModel(Security security)
        {
            return new MexcFeeModel();
        }

        protected static IReadOnlyDictionary<SecurityType, string> GetDefaultMarkets(string marketName)
        {
            var map = DefaultBrokerageModel.DefaultMarketMap.ToDictionary();
            map[SecurityType.Crypto] = marketName;
            map[SecurityType.CryptoFuture] = marketName;
            return map.ToReadOnlyDictionary();
        }

        public override bool CanSubmitOrder(Security security, Orders.Order order, out BrokerageMessageEvent message)
        {
            message = null;

            if (security.Type != SecurityType.Base && security.Type != SecurityType.Crypto && security.Type != SecurityType.CryptoFuture)
            {
                message = new BrokerageMessageEvent(BrokerageMessageType.Warning, "NotSupported",
                    Messages.DefaultBrokerageModel.UnsupportedSecurityType(this, security));

                return false;
            }
            return base.CanSubmitOrder(security, order, out message);
        }
    }
}

import requests
import pandas as pd
from datetime import datetime, timedelta
import time
import json


def get_sol_usdt_futures_bars():
    """
    Get SOLUSDT Futures 1-minute bars for the latest 2 hours
    """
    # MEXC Contract API base URL
    base_url = "https://contract.mexc.com"
    
    # Calculate time range for latest 2 hours
    end_time = int(time.time() * 1000)  # Current time in milliseconds
    start_time = end_time - (2 * 60 * 60 * 1000)  # 2 hours ago in milliseconds
    
    # Try different endpoint formats
    endpoints_to_try = [
        f"{base_url}/api/v1/contract/kline/SOL_USDT",
        f"{base_url}/api/v1/contract/kline",
        f"{base_url}/api/v1/contract/kline/SOLUSDT",
        f"{base_url}/api/v1/contract/kline/SOL_USDT"
    ]
    
    # Try different parameter formats
    param_sets = [
        {
            "interval": "1m",
            "start": str(start_time),
            "end": str(end_time),
            "limit": "1000"
        },
        {
            "symbol": "SOL_USDT",
            "interval": "1m",
            "start": str(start_time),
            "end": str(end_time),
            "limit": "1000"
        },
        {
            "symbol": "SOLUSDT",
            "interval": "1m",
            "start": str(start_time),
            "end": str(end_time),
            "limit": "1000"
        },
        {
            "interval": "1m",
            "start": start_time,
            "end": end_time,
            "limit": 1000
        }
    ]
    
    for i, endpoint in enumerate(endpoints_to_try):
        for j, params in enumerate(param_sets):
            print(f"\nTrying endpoint {i+1}, parameter set {j+1}:")
            print(f"Endpoint: {endpoint}")
            print(f"Parameters: {params}")
            
            try:
                response = requests.get(endpoint, params=params, timeout=10)
                print(f"Response status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"Response data: {data}")
                    
                    if data.get("success"):
                        kline_data = data.get("data", [])
                        
                        if kline_data:
                            # Convert to DataFrame
                            df = pd.DataFrame(kline_data, columns=[
                                'timestamp', 'open', 'high', 'low', 'close', 'volume', 'amount'
                            ])
                            
                            # Convert timestamp to datetime
                            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                            
                            # Convert string values to numeric
                            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
                            for col in numeric_columns:
                                df[col] = pd.to_numeric(df[col], errors='coerce')
                            
                            # Sort by timestamp
                            df = df.sort_values('timestamp')
                            
                            print(f"SUCCESS! Retrieved {len(df)} 1-minute bars for SOLUSDT Futures")
                            print(f"Time range: {df['timestamp'].min()} to {df['timestamp'].max()}")
                            
                            return df
                        else:
                            print("No data returned from API")
                    else:
                        print(f"API request failed: {data.get('message', 'Unknown error')}")
                else:
                    print(f"HTTP Error: {response.status_code}")
                    print(f"Response: {response.text}")
                    
            except Exception as e:
                print(f"Error: {e}")
                continue
    
    print("All REST API attempts failed")
    return None


def get_sol_usdt_futures_bars_websocket():
    """
    Alternative: Get real-time SOLUSDT Futures data via WebSocket
    This collects 2 hours of real-time data
    """
    import websockets
    import asyncio
    from collections import deque
    import threading
    
    URI = "wss://contract.mexc.com/edge"
    
    # Store for collecting data
    data_store = deque(maxlen=120)  # Store max 120 minutes (2 hours)
    
    async def websocket_handler():
        try:
            async with websockets.connect(URI, ping_interval=None) as web:
                # Subscribe to SOL_USDT deal data
                subscription = {
                    "method": "sub.deal",
                    "param": {"symbol": "SOL_USDT"}
                }
                
                await web.send(json.dumps(subscription))
                print("Subscribed to SOL_USDT real-time data")
                
                start_time = time.time()
                
                while True:
                    try:
                        response = await web.recv()
                        data = json.loads(response)
                        
                        # Handle subscription response
                        if data.get("channel") == "rs.sub.deal":
                            print("Successfully subscribed to deal data")
                            continue
                            
                        # Handle ping-pong
                        if data.get("method") == "ping":
                            await web.send(json.dumps({"method": "pong"}))
                            continue
                            
                        # Process deal data
                        if data.get("channel") == "push.deal":
                            deal_data = data.get("data", {})
                            
                            # Create bar data structure
                            bar_data = {
                                'timestamp': data.get("ts"),
                                'price': float(deal_data.get("p", 0)),
                                'volume': float(deal_data.get("v", 0)),
                                'side': deal_data.get("T")  # T for trade side
                            }
                            
                            data_store.append(bar_data)
                            
                            # Print progress every 10 minutes
                            elapsed = time.time() - start_time
                            if len(data_store) % 10 == 0:
                                print(f"Collected {len(data_store)} data points, elapsed: {elapsed:.1f}s")
                            
                            # Stop after 2 hours
                            if elapsed >= 7200:  # 2 hours in seconds
                                print("Data collection complete")
                                break
                                
                    except Exception as e:
                        print(f"WebSocket error: {e}")
                        break
                        
        except Exception as e:
            print(f"Connection error: {e}")
    
    # Run WebSocket in separate thread
    def run_websocket():
        asyncio.run(websocket_handler())
    
    thread = threading.Thread(target=run_websocket)
    thread.start()
    
    # Wait for completion
    thread.join()
    
    return list(data_store)


def test_websocket_connection():
    """
    Test WebSocket connection and get some real-time data
    """
    import websockets
    import asyncio
    
    URI = "wss://contract.mexc.com/edge"
    
    async def test_websocket():
        try:
            async with websockets.connect(URI, ping_interval=None) as web:
                subscription = {
                    "method": "sub.deal",
                    "param": {"symbol": "SOL_USDT"}
                }
                
                await web.send(json.dumps(subscription))
                print("WebSocket connection successful")
                print("Subscribed to SOL_USDT real-time data")
                
                # Collect some data for testing
                collected_data = []
                start_time = time.time()
                
                while len(collected_data) < 10 and (time.time() - start_time) < 30:
                    try:
                        response = await asyncio.wait_for(web.recv(), timeout=5.0)
                        data = json.loads(response)
                        
                        # Handle subscription response
                        if data.get("channel") == "rs.sub.deal":
                            print("Successfully subscribed to deal data")
                            continue
                            
                        # Handle ping-pong
                        if data.get("method") == "ping":
                            await web.send(json.dumps({"method": "pong"}))
                            continue
                            
                        # Process deal data
                        if data.get("channel") == "push.deal":
                            deal_data = data.get("data", {})
                            
                            bar_data = {
                                'timestamp': data.get("ts"),
                                'price': float(deal_data.get("p", 0)),
                                'volume': float(deal_data.get("v", 0)),
                                'side': deal_data.get("T")
                            }
                            
                            collected_data.append(bar_data)
                            print(f"Collected data point {len(collected_data)}: {bar_data}")
                            
                    except asyncio.TimeoutError:
                        print("Timeout waiting for data")
                        break
                    except Exception as e:
                        print(f"Error receiving data: {e}")
                        break
                
                print(f"Collected {len(collected_data)} data points")
                return collected_data
                
        except Exception as e:
            print(f"WebSocket test failed: {e}")
            return []
    
    return asyncio.run(test_websocket())


if __name__ == "__main__":
    print("=== MEXC SOLUSDT Futures 1-Minute Bars ===")
    print("Method 1: REST API (Historical Data)")
    print("-" * 50)
    
    # Get historical data via REST API
    df_historical = get_sol_usdt_futures_bars()
    
    if df_historical is not None:
        print(f"\nData shape: {df_historical.shape}")
        print(f"Columns: {list(df_historical.columns)}")
        
        # Save to CSV
        filename = f"SOLUSDT_1m_bars_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df_historical.to_csv(filename, index=False)
        print(f"Data saved to: {filename}")
    
    print("\n" + "=" * 50)
    print("Method 2: WebSocket (Real-time Data Collection)")
    print("-" * 50)
    print("Testing WebSocket connection...")
    
    # Test WebSocket connection
    websocket_data = test_websocket_connection()
    
    if websocket_data:
        print(f"\nWebSocket test successful! Collected {len(websocket_data)} data points")
        print("Sample data:")
        for i, data in enumerate(websocket_data[:3]):
            print(f"  {i+1}: {data}")
    
    print("\nTo collect 2 hours of real-time data, uncomment:")
    print("# websocket_data = get_sol_usdt_futures_bars_websocket()") 
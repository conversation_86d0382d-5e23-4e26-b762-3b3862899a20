﻿using System;
using System.Collections.Generic;
using QuantConnect.Brokerages;
using QuantConnect.Configuration;
using QuantConnect.Interfaces;
using QuantConnect.Packets;
using QuantConnect.Securities;

namespace QuantConnect.Brokerages.Mexc
{
    public class MexcBrokerageFactory : BrokerageFactory
    {
        public override Dictionary<string, string> BrokerageData => new Dictionary<string, string>
        {
            { "mexc-api-key", Config.Get("mexc-api-key", "")},
            { "mexc-api-secret", Config.Get("mexc-api-secret", "")},
            { "mexc-websocket-url", Config.Get("mexc-websocket-url", "wss://contract.mexc.com/edge")},
        };

        public MexcBrokerageFactory() : base(typeof(MexcBrokerage))
        {
        }

        public override IBrokerageModel GetBrokerageModel(IOrderProvider orderProvider)
        {
            return new MexcBrokerageModel(AccountType.Cash);
        }

        public override IBrokerage CreateBrokerage(LiveNodePacket job, IAlgorithm algorithm)
        {
            var brokerage = new MexcBrokerage();
            return brokerage;
        }

        public override void Dispose()
        {
        }
    }
}
using System;
using QuantConnect.Benchmarks;
using QuantConnect.Orders.Fees;
using QuantConnect.Securities;
using QuantConnect.Securities.CryptoFuture;
using QuantConnect.Util;

namespace QuantConnect.Brokerages
{
    public class MexcFuturesBrokerageModel : MexcBrokerageModel
    {
        private const decimal _defaultFutureLeverage = 25;

        public MexcFuturesBrokerageModel(AccountType accountType) : base(accountType)
        {
            if (accountType == AccountType.Cash)
            {
                throw new InvalidOperationException($"{SecurityType.CryptoFuture} can only be traded using a {AccountType.Margin} account type");
            }
        }

        public override decimal GetLeverage(Security security)
        {
            if (security.IsInternalFeed() || security.Type == SecurityType.Base)
            {
                return 1m;
            }

            return security.Symbol.SecurityType == SecurityType.CryptoFuture ? _defaultFutureLeverage : base.GetLeverage(security);
        }

        public override IBenchmark GetBenchmark(SecurityManager securities)
        {
            return new FuncBenchmark(x => 0);
        }

        public override IFeeModel GetFeeModel(Security security)
        {
            return new MexcFeeModel();
        }

        public override IMarginInterestRateModel GetMarginInterestRateModel(Security security)
        {
            if (security.Symbol.SecurityType == SecurityType.CryptoFuture && security.Symbol.ID.Date == SecurityIdentifier.DefaultDate)
            {
                return new MexcFutureMarginInterestRateModel();
            }
            return base.GetMarginInterestRateModel(security);
        }
    }
}

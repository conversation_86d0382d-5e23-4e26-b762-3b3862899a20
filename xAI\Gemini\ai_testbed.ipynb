{"cells": [{"cell_type": "markdown", "metadata": {"id": "UzBKAaL4QYq0"}, "source": ["##### Install SDK"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "IbKkL5ksQYq1"}, "outputs": [], "source": ["%pip install -U -q \"google-genai\""]}, {"cell_type": "markdown", "metadata": {"id": "aDUGen_kQYq2"}, "source": ["##### Initialize"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0H_lRdlrQYq3"}, "outputs": [], "source": ["import os\n", "import sys\n", "from google import genai\n", "from google.genai import types\n", "import json\n", "from PIL import Image\n", "from IPython.display import display, Markdown\n", "from google.genai.types import Tool, GenerateContentConfig, GoogleSearch\n", "\n", "GOOGLE_API_KEY=os.getenv(\"GOOGLE_API_KEY\")\n", "client = genai.Client(api_key=GOOGLE_API_KEY)\n", "MODEL_ID = \"gemini-2.5-pro-exp-03-25\" # @param [\"gemini-1.5-flash-latest\",\"gemini-2.0-flash-lite\",\"gemini-2.0-flash\",\"gemini-2.0-pro-exp-02-05\",\"gemini-2.0-flash-thinking-exp-01-21\"] {\"allow-input\":true}"]}, {"cell_type": "markdown", "metadata": {"id": "GAa7sCD7tuMW"}, "source": ["##### 1. Code Simplification"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "PZw41-lsKKMf"}, "outputs": [], "source": ["response = client.models.generate_content(\n", "    model=MODEL_ID,\n", "    contents='How can I simplify this? `(Math.round(radius/pixelsPerMile * 10) / 10).toFixed(1);`.'\n", ")\n", "\n", "Markdown(response.text)"]}, {"cell_type": "markdown", "metadata": {"id": "vhmMThinkingSearch"}, "source": ["##### 2. Thinking with Search Tool"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "qFp3r_dpv5di"}, "outputs": [], "source": ["\n", "prompt = \"\"\"show me the latest 3 git commits of Google Chromium project\"\"\"\n", "contents = [\n", "    types.Content(\n", "        role=\"user\",\n", "        parts=[\n", "            types.Part.from_text(text=prompt),\n", "        ],\n", "    ),\n", "]\n", "tools = [types.Tool(google_search=types.GoogleSearch())]\n", "generate_content_config = types.GenerateContentConfig(\n", "    tools=tools,\n", "    response_mime_type=\"text/plain\",\n", ")\n", "\n", "final_output = ''\n", "for chunk in client.models.generate_content_stream(\n", "    model=MODEL_ID,\n", "    contents=contents,\n", "    config=generate_content_config,\n", "):\n", "    final_output += chunk.text\n", "\n", "Markdown(final_output)"]}, {"cell_type": "markdown", "metadata": {"id": "ADiJV-fFyjRe"}, "source": ["##### 3. Geometry Problem (with Image)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MIcXWXqyzCjQ"}, "outputs": [], "source": ["!curl -o geometry.png -s \"https://storage.googleapis.com/generativeai-downloads/images/geometry.png\"\n", "\n", "im = Image.open(\"geometry.png\").resize((256,256))\n", "response = client.models.generate_content(\n", "    model=MODEL_ID,\n", "    contents=[\n", "        im,\n", "        \"What's the area of the overlapping region?\"\n", "    ]\n", ")\n", "\n", "Markdown(response.text)"]}, {"cell_type": "markdown", "metadata": {"id": "EXPPWpt6ttJZ"}, "source": ["##### 4. <PERSON> with a Twist"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "F2YeBqzC0J_i"}, "outputs": [], "source": ["!curl -o pool.png -s \"https://storage.googleapis.com/generativeai-downloads/images/pool.png\"\n", "\n", "im = Image.open(\"pool.png\")\n", "response = client.models.generate_content(\n", "    model=MODEL_ID,\n", "    contents=[\n", "        im,\n", "        \"How do I use three of the pool balls to sum up to 30?\"\n", "    ]\n", ")\n", "\n", "Markdown(response.text)"]}, {"cell_type": "markdown", "metadata": {"id": "dtBDPf4kAyG1"}, "source": ["##### 5. with Streaming: Generating Question for a Specific Level of Knowledge"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0mjH0A8HBQ5d"}, "outputs": [], "source": ["response = client.models.generate_content_stream(\n", "    model=MODEL_ID,\n", "    contents=\"Give me a practice question I can use for the AP Physics C exam?\"\n", ")\n", "\n", "for chunk in response:\n", "  for part in chunk.candidates[0].content.parts:\n", "    display(Markdown(part.text))"]}, {"cell_type": "markdown", "metadata": {"id": "CPI_TTHKw3ob"}, "source": ["##### 6. Thinking with Code Execution\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ynozGcOFxI6P"}, "outputs": [], "source": ["prompt = (\n", "    \"What is the sum of the first 50 prime numbers? \"\n", "    \"Generate and run Python code for the calculation, and make sure you get all 50.\"\n", "    \"Provide the final sum clearly.\"\n", ")\n", "\n", "code_execution_tool = types.Tool(\n", "    code_execution=types.ToolCodeExecution() # Enable code execution tool\n", ")\n", "\n", "response = client.models.generate_content(\n", "    model=MODEL_ID,\n", "    contents=prompt,\n", "    config=types.GenerateContentConfig(\n", "        tools=[code_execution_tool], # Provide the tool in the config\n", "    ),\n", ")\n", "\n", "# Check if the model decided to execute code\n", "if response.candidates and response.candidates[0].content.parts:\n", "    executed_code = False\n", "    for part in response.candidates[0].content.parts:\n", "        if hasattr(part, 'executable_code'):\n", "            print(\"\\nExecutable Code Generated:\")\n", "            # Ensure executable_code object itself exists before accessing language/code\n", "            if part.executable_code:\n", "                print(f\"Language: {part.executable_code.language}\")\n", "                print(f\"Code:\\n{part.executable_code.code}\")\n", "            else:\n", "                print(\"(Executable code part found, but content is empty)\")\n", "                executed_code = True\n", "        if hasattr(part, 'code_execution_result'):\n", "            print(\"\\nCode Execution Result:\")\n", "            # Ensure code_execution_result object exists before accessing outcome/output\n", "            if part.code_execution_result:\n", "                print(f\"Outcome: {part.code_execution_result.outcome}\")\n", "                print(f\"Output:\\n{part.code_execution_result.output}\")\n", "            else:\n", "                print(\"(Code execution result part found, but content is empty)\")\n", "                executed_code = True # Mark as executed even if result is empty/error\n", "        <PERSON><PERSON>(part, 'text'): # Print regular text parts too\n", "            print(\"\\nModel Text Response:\")\n", "            print(part.text)\n", "\n", "        if not executed_code:\n", "            print(\"\\nModel did not generate or execute code for this prompt.\")\n", "            # Print the plain text response if no code parts were found\n", "            print(\"\\nFull Text Response:\")\n", "            print(response.text)\n", "else:\n", "    print(\"\\nNo response parts found.\")\n", "    # Print finish reason if available\n", "    if response.candidates and response.candidates[0].finish_reason:\n", "        print(f\"Finish Reason: {response.candidates[0].finish_reason.name}\")\n", "    elif response.prompt_feedback:\n", "        print(f\"Prompt Feedback: {response.prompt_feedback}\")"]}, {"cell_type": "markdown", "metadata": {"id": "B30j4o0JymWU"}, "source": ["##### 7. Thinking with Structured Outputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "nmVmMQxsytV2"}, "outputs": [], "source": ["prompt = (\n", "    \"Provide a list of 3 famous physicists and their key contributions \"\n", "    \"in JSON format.\\n\\n\"\n", "    \"Use this JSON schema:\\n\\n\"\n", "    \"Physicist = {'name': str, 'contribution': str, 'era': str}\\n\"\n", "    \"Return: list[Physicist]\"\n", ")\n", "\n", "# For basic JSON output, often no special config is needed if the prompt is clear.\n", "# For more robust JSON mode (forcing JSON output and schema validation),\n", "# you would use response_mime_type=\"application/json\" and potentially json_schema\n", "# in GenerateContentConfig (currently might require specific model versions or flags).\n", "# This example relies on prompt instruction.\n", "response = client.models.generate_content(\n", "     model=MODEL_ID,\n", "     contents=prompt,\n", "     # Example for stricter JSON mode (if supported and desired):\n", "            # config=types.GenerateContentConfig(\n", "            #     response_mime_type=\"application/json\",\n", "            #     # json_schema=... # Define schema object here if needed\n", "            # )\n", ")\n", "\n", "\n", "# The response.text should contain the JSON string\n", "print(response.text)"]}], "metadata": {"colab": {"name": "Get_started_thinking.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 0}
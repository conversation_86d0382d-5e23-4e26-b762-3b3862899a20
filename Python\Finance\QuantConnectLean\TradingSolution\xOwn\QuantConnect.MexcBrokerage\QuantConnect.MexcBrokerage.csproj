<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Release</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <TargetFramework>net9.0</TargetFramework>
    <Product>QuantConnect.Brokerages.Mexc</Product>
    <AssemblyName></AssemblyName>
    <RootNamespace></RootNamespace>
    <AssemblyTitle>QuantConnect.Brokerages.Mexc</AssemblyTitle>
    <OutputType>Library</OutputType>
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <Description>QuantConnect LEAN Mexc Brokerage: Brokerage Mexc plugin for Lean</Description>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugType>full</DebugType>
    <OutputPath>bin\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <OutputPath>bin\Release\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\..\Lean\Common\Properties\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="QuantConnect.Brokerages" Version="2.5.*" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Lean\Common\QuantConnect.csproj" />
  </ItemGroup>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy /Y &quot;$(TargetPath)&quot; $(SolutionDir)Launcher\bin\$(Configuration)\&#xD;&#xA;&#xD;&#xA;if exist &quot;D:\work\xstarwalker168\Python\Finance\QuantConnectLean\QC-Log-Dir&quot; del /q &quot;D:\work\xstarwalker168\Python\Finance\QuantConnectLean\QC-Log-Dir\*&quot;&#xD;&#xA;" />
  </Target>
</Project>
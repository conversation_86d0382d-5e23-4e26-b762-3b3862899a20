{"cells": [{"cell_type": "markdown", "metadata": {"id": "UzBKAaL4QYq0"}, "source": ["##### Make a request from Python"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "IbKkL5ksQYq1"}, "outputs": [], "source": ["# In your terminal, first run:\n", "# pip install openai\n", "\n", "import os\n", "from IPython.display import display, Markdown\n", "from openai import OpenAI\n", "\n", "XAI_API_KEY = os.getenv(\"XAI_API_KEY\")\n", "client = OpenAI(\n", "    api_key=XAI_API_KEY,\n", "    base_url=\"https://api.x.ai/v1\",\n", ")\n", "\n", "completion = client.chat.completions.create(\n", "    model=\"grok-3-beta\",\n", "    messages=[\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"You are <PERSON><PERSON>, a highly intelligent, helpful AI assistant.\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"the latest NVDA stock price\"\n", "        },\n", "    ],\n", ")\n", "\n", "Markdown(completion.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {"id": "aDUGen_kQYq2"}, "source": ["##### A Basic Chat Completions Example"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0H_lRdlrQYq3"}, "outputs": [], "source": ["import os\n", "from openai import OpenAI\n", "from IPython.display import display, Markdown\n", "\n", "XAI_API_KEY = os.getenv(\"XAI_API_KEY\")\n", "client = OpenAI(\n", "    api_key=XAI_API_KEY,\n", "    base_url=\"https://api.x.ai/v1\",\n", ")\n", "\n", "completion = client.chat.completions.create(\n", "    model=\"grok-3-latest\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"You are a PhD-level mathematician.\"},\n", "        {\"role\": \"user\", \"content\": \"What is 2 + 2?\"},\n", "    ],\n", ")\n", "\n", "print(completion.choices[0].message)"]}, {"cell_type": "markdown", "metadata": {"id": "GAa7sCD7tuMW"}, "source": ["##### Reasoning"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "PZw41-lsKKMf"}, "outputs": [], "source": ["import os\n", "from openai import OpenAI\n", "\n", "messages = [\n", "    {\n", "        \"role\": \"system\",\n", "        \"content\": \"You are a highly intelligent AI assistant.\",\n", "    },\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"What is 101*3?\",\n", "    },\n", "]\n", "\n", "client = OpenAI(\n", "    base_url=\"https://api.x.ai/v1\",\n", "    api_key=os.getenv(\"XAI_API_KEY\"),\n", ")\n", "\n", "completion = client.chat.completions.create(\n", "    model=\"grok-3-mini-beta\", # or \"grok-3-mini-beta\", \"grok-3-mini-fast-beta\"\n", "    reasoning_effort=\"high\",\n", "    messages=messages,\n", "    temperature=0.7,\n", ")\n", "\n", "print(\"Reasoning Content:\")\n", "print(completion.choices[0].message.reasoning_content)\n", "\n", "print(\"\\nFinal Response:\")\n", "print(completion.choices[0].message.content)\n", "\n", "print(\"\\nNumber of completion tokens (input):\")\n", "print(completion.usage.completion_tokens)\n", "\n", "print(\"\\nNumber of reasoning tokens (input):\")\n", "print(completion.usage.completion_tokens_details.reasoning_tokens)"]}, {"cell_type": "markdown", "metadata": {"id": "vhmMThinkingSearch"}, "source": ["##### Deferred Chat Completions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "qFp3r_dpv5di"}, "outputs": [], "source": ["import json\n", "import os\n", "import requests\n", "\n", "from tenacity import retry, wait_exponential\n", "\n", "headers = {\n", "    \"Content-Type\": \"application/json\",\n", "    \"Authorization\": f\"Bearer {os.getenv('XAI_API_KEY')}\"\n", "}\n", "\n", "payload = {\n", "    \"messages\": [\n", "        {\"role\": \"system\", \"content\": \"You are <PERSON><PERSON><PERSON>.\"},\n", "        {\"role\": \"user\", \"content\": \"126/3=?\"}\n", "    ],\n", "    \"model\": \"grok-3-latest\",\n", "    \"deferred\": True\n", "}\n", "\n", "response = requests.post(\n", "    \"https://api.x.ai/v1/chat/completions\",\n", "    headers=headers,\n", "    json=payload\n", ")\n", "request_id = response.json()[\"request_id\"]\n", "print(f\"Request ID: {request_id}\")\n", "\n", "@retry(wait=wait_exponential(multiplier=1, min=1, max=60),)\n", "def get_deferred_completion():\n", "    response = requests.get(f\"https://api.x.ai/v1/chat/deferred-completion/{request_id}\", headers=headers)\n", "    if response.status_code == 200:\n", "        return response.json()\n", "    elif response.status_code == 202:\n", "        raise Exception(\"Response not ready yet\")\n", "    else:\n", "        raise Exception(f\"{response.status_code} Error: {response.text}\")\n", "\n", "completion_data = get_deferred_completion()\n", "print(json.dumps(completion_data, indent=4))"]}, {"cell_type": "markdown", "metadata": {"id": "ADiJV-fFyjRe"}, "source": ["##### Function calling"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MIcXWXqyzCjQ"}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "from typing import Literal\n", "\n", "# Defining functions and function arguments\n", "class TemperatureRequest(BaseModel):\n", "    location: str = Field(description=\"The city and state, e.g. San Francisco, CA\")\n", "    unit: Literal[\"celsius\", \"fahrenheit\"] = Field(\n", "        \"fahrenheit\", description=\"Temperature unit\"\n", "    )\n", "\n", "class CeilingRequest(BaseModel):\n", "    location: str = Field(description=\"The city and state, e.g. San Francisco, CA\")\n", "\n", "def get_current_temperature(**kwargs):\n", "    request = TemperatureRequest(**kwargs)\n", "    temperature: int\n", "    if request.unit.lower() == \"fahrenheit\":\n", "        temperature = 59\n", "    elif request.unit.lower() == \"celsius\":\n", "        temperature = 15\n", "    else:\n", "        raise ValueError(\"unit must be one of fahrenheit or celsius\")\n", "    return {\n", "        \"location\": request.location,\n", "        \"temperature\": temperature,\n", "        \"unit\": request.unit.lower(),\n", "    }\n", "\n", "def get_current_ceiling(**kwargs):\n", "    request = CeilingRequest(**kwargs)\n", "    return {\n", "        \"location\": request.location,\n", "        \"ceiling\": 15000,\n", "        \"ceiling_type\": \"broken\",\n", "        \"unit\": \"ft\",\n", "    }\n", "\n", "\n", "# Generate the JSON schema\n", "get_current_temperature_schema = TemperatureRequest.model_json_schema()\n", "get_current_ceiling_schema = CeilingRequest.model_json_schema()\n", "\n", "# Definition of parameters with Pydantic JSON schema\n", "tools_definition = [\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"get_current_temperature\",\n", "            \"description\": \"Get the current temperature in a given location\",\n", "            \"parameters\": get_current_temperature_schema,\n", "        },\n", "    },\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"get_current_ceiling\",\n", "            \"description\": \"Get the current cloud ceiling in a given location\",\n", "            \"parameters\": get_current_ceiling_schema,\n", "        },\n", "    },\n", "]\n", "\n", "tools_map = {\n", "    \"get_current_temperature\": get_current_temperature,\n", "    \"get_current_ceiling\": get_current_ceiling,\n", "}\n", "\n", "\n", "messages = [{\"role\": \"user\", \"content\": \"What's the temperature like in San Francisco?\"}]\n", "response = client.chat.completions.create(\n", "    model=\"grok-3-latest\",\n", "    messages=messages,\n", "    tools=tools_definition,  # The dictionary of our functions and their parameters\n", "    tool_choice=\"auto\",\n", ")\n", "\n", "# You can inspect the response which contains a tool call\n", "print(response.choices[0].message)\n", "\n", "# Append assistant message including tool calls to messages\n", "messages.append(response.choices[0].message)\n", "\n", "# Check if there is any tool calls in response body\n", "# You can also wrap this in a function to make the code cleaner\n", "\n", "if response.choices[0].message.tool_calls:\n", "    for tool_call in response.choices[0].message.tool_calls:\n", "\n", "        # Get the tool function name and arguments <PERSON><PERSON> wants to call\n", "        function_name = tool_call.function.name\n", "        function_args = json.loads(tool_call.function.arguments)\n", "\n", "        # Call one of the tool function defined earlier with arguments\n", "        result = tools_map[function_name](**function_args)\n", "\n", "        # Append the result from tool function call to the chat message history,\n", "        # with \"role\": \"tool\"\n", "        messages.append(\n", "            {\n", "                \"role\": \"tool\",\n", "                \"content\": json.dumps(result),\n", "                \"tool_call_id\": tool_call.id  # tool_call.id supplied in <PERSON><PERSON>'s response\n", "            }\n", "        )\n", "\n", "        response = client.chat.completions.create(\n", "            model=\"grok-3-latest\",\n", "            messages=messages,\n", "            tools=tools_definition,\n", "            tool_choice=\"auto\"\n", "        )\n", "\n", "        print(response.choices[0].message.content)\n"]}], "metadata": {"colab": {"name": "Get_started_thinking.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 0}
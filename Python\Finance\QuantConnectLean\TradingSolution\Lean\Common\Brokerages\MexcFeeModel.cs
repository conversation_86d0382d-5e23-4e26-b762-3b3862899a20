using QuantConnect.Orders;
using QuantConnect.Orders.Fees;
using QuantConnect.Securities;
using QuantConnect.Util;

namespace QuantConnect.Brokerages
{
    public class MexcFeeModel : FeeModel
    {
        private const decimal _makerFee = 0.0002m;
        private const decimal _takerFee = 0.0006m;

        public override OrderFee GetOrderFee(OrderFeeParameters parameters)
        {
            var security = parameters.Security;
            var order = parameters.Order;

            var fee = GetFee(order);

            if (security.Symbol.ID.SecurityType == SecurityType.CryptoFuture)
            {
                var positionValue = security.Holdings.GetQuantityValue(order.AbsoluteQuantity, security.Price);
                return new OrderFee(new CashAmount(positionValue.Amount * fee, positionValue.Cash.Symbol));
            }

            if (order.Direction == OrderDirection.Buy)
            {
                CurrencyPairUtil.DecomposeCurrencyPair(order.Symbol, out var baseCurrency, out _);
                return new OrderFee(new CashAmount(order.AbsoluteQuantity * fee, baseCurrency));
            }

            var unitPrice = order.Direction == OrderDirection.Buy ? security.AskPrice : security.BidPrice;
            if (order.Type == OrderType.Limit)
            {
                unitPrice = ((LimitOrder)order).LimitPrice;
            }

            unitPrice *= security.SymbolProperties.ContractMultiplier;

            return new OrderFee(new CashAmount(
                unitPrice * order.AbsoluteQuantity * fee,
                security.QuoteCurrency.Symbol));
        }

        private static decimal GetFee(Order order)
        {
            var fee = _takerFee;

            if (order.Type == OrderType.Limit && !order.IsMarketable)
            {
                fee = _makerFee;
            }

            return fee;
        }
    }
}
